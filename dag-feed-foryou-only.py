import datetime

from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.dataproc import DataprocSubmitJobOperator
from airflow.utils.dates import days_ago
from slack_alert import dag_fail_slack_alert

# Date variables
date = days_ago(1).strftime('%Y-%m-%d')
date_nodash = days_ago(1).strftime('%Y%m%d')
today_date = days_ago(0).strftime('%Y-%m-%d')
today_nodash = days_ago(0).strftime('%Y%m%d')

# Configuration
project_id = "butterflies-ai"
cluster_region = "us-central1"
cluster_name = "cluster-data3"
job_dir = "gs://dataproc-butterflies/bt-jobs"

default_args = {
    "owner": "Mehrdad",
    "depends_on_past": False,
    "email": "<EMAIL>",
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(minutes=5),
    "start_date": days_ago(1),
}

dag = DAG(
    "feed_foryou_only",
    description='Run only the feed_foryou job',
    catchup=False,
    default_args=default_args,
    schedule_interval=None,  # Manual trigger only
    on_failure_callback=dag_fail_slack_alert,
)

# Define the feed function
def feed(table_name: str, feed_days_ago: int):
    feed_start_date_nodash = days_ago(feed_days_ago).strftime('%Y%m%d')
    return BigQueryExecuteQueryOperator(
        task_id=f"{table_name}",
        destination_dataset_table=f"butterflies-ai.db_computed.{table_name}_{today_nodash}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_TRUNCATE',
        params={'date_nodash': f"{today_nodash}", 'days_ago': f"{feed_days_ago}", 'start_date_nodash': f"{feed_start_date_nodash}"},
        dag=dag,
    )

# Define the run_job function
def run_job(job_name: str):
    return DataprocSubmitJobOperator(
        task_id=job_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': job_name},
            "pyspark_job": {
                "main_python_file_uri": f"{job_dir}/{job_name}.py",
                "python_file_uris": ["gs://dataproc-butterflies/protos.zip"],
                "args": [date],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

# Create the tasks
begin_feed = DummyOperator(
    task_id='begin_feed',
    dag=dag,
)

end = DummyOperator(
    task_id='end', 
    dag=dag
)

# Create the specific job chain you want to run
feed_foryou_task = feed("feed_foryou", 14)
knn_foryou_task = run_job("bt-knn-foryou-id")

# Define the task dependencies - exactly as you selected
begin_feed >> feed_foryou_task >> knn_foryou_task >> end
