import datetime
from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.providers.google.cloud.operators.dataproc import DataprocSubmitJobOperator
from airflow.utils.dates import days_ago
from slack_alert import dag_fail_slack_alert

# Date variables
date = days_ago(1).strftime('%Y-%m-%d')
today_nodash = days_ago(0).strftime('%Y%m%d')

# Configuration
project_id = "butterflies-ai"
cluster_region = "us-central1"
cluster_name = "cluster-data3"
job_dir = "gs://dataproc-butterflies/bt-jobs"

default_args = {
    "owner": "Mehr<PERSON>",
    "depends_on_past": False,
    "email": "<EMAIL>",
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(minutes=5),
    "start_date": days_ago(1),
}

dag = DAG(
    "bt_knn_foryou_id",
    description='Process ForYou feed data and write to Bigtable',
    catchup=False,
    default_args=default_args,
    schedule_interval=None,  # Manual trigger only
    on_failure_callback=dag_fail_slack_alert,
)

start = DummyOperator(
    task_id='start',
    dag=dag,
)

end = DummyOperator(
    task_id='end',
    dag=dag,
)

# Main task that runs the bt-knn-foryou-id.py script
bt_knn_foryou_task = DataprocSubmitJobOperator(
    task_id='bt_knn_foryou_id_job',
    job={
        "placement": {"cluster_name": cluster_name},
        'labels': {'job': 'bt-knn-foryou-id'},
        "pyspark_job": {
            "main_python_file_uri": f"{job_dir}/bt-knn-foryou-id.py",
            "python_file_uris": ["gs://dataproc-butterflies/protos.zip"],
            "args": [date],  # Pass yesterday's date as argument
        },
    },
    region=cluster_region,
    project_id=project_id,
    dag=dag,
)

# Optional task with only_new parameter
bt_knn_foryou_only_new_task = DataprocSubmitJobOperator(
    task_id='bt_knn_foryou_id_only_new',
    job={
        "placement": {"cluster_name": cluster_name},
        'labels': {'job': 'bt-knn-foryou-id-only-new'},
        "pyspark_job": {
            "main_python_file_uri": f"{job_dir}/bt-knn-foryou-id.py",
            "python_file_uris": ["gs://dataproc-butterflies/protos.zip"],
            "args": [date, "only_new"],  # Pass date and only_new flag
        },
    },
    region=cluster_region,
    project_id=project_id,
    dag=dag,
)

# Define task dependencies
start >> bt_knn_foryou_task >> end
start >> bt_knn_foryou_only_new_task >> end
