import datetime
import json
import base64
from collections import defaultdict
from datetime import <PERSON><PERSON><PERSON>

from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.bigtable import BigtableHook
from airflow.utils.dates import days_ago
from slack_alert import dag_fail_slack_alert

# Date variables
yesterday_date = days_ago(1)
today_nodash = days_ago(0).strftime('%Y%m%d')
project_id = "butterflies-ai"

default_args = {
    "owner": "Mehrdad",
    "depends_on_past": False,
    "email": "<EMAIL>",
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(minutes=5),
    "start_date": days_ago(1),
}

dag = DAG(
    "bt_knn_foryou_id_standalone",
    description='Process ForYou feed data and write to Bigtable (standalone)',
    catchup=False,
    default_args=default_args,
    schedule_interval=None,  # Manual trigger only
    on_failure_callback=dag_fail_slack_alert,
)

def read_user_post_data(**context):
    """Read user post data from BigQuery"""
    date_nodash = context['ds_nodash']

    query_foryou = f"""
        --bt-knn-foryou-id.py
        with post_reads as (
        select profile_id, max(created_at) as created_at, min(DATE_DIFF(date(CURRENT_TIMESTAMP()), date(created_at), DAY)) as days_ago
        from `db.post_reads`
        group by 1
        ), profiles as (
        select profile_id
        from (select id as profile_id from db.profiles where created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        union all
        select profile_id from post_reads where FLOOR(RAND() * days_ago / 7)=0
        union all
        select -1 as profile_id
        union all
        select -2 as profile_id
        union all
        select -3 as profile_id
        )
        group by 1
        )

        SELECT f.profile_id, array_agg(struct(post_id, reason, label) order by score desc limit 400) as posts
        FROM `butterflies-ai.db_computed.feed_foryou_{date_nodash}` f
        join profiles p on p.profile_id = f.profile_id
        group by 1;
    """

    reason_to_enum = {
        "suggested": 0,  # feed_pb2.FeedType.suggested
        "created_by": 1,  # feed_pb2.FeedType.created_by
        "followed_by": 2,  # feed_pb2.FeedType.followed_by
        "similar_to": 3,  # feed_pb2.FeedType.similar_to
        "tagged_by": 4,  # feed_pb2.FeedType.tagged_by
        "trending": 5,  # feed_pb2.FeedType.trending
        "private_eyes": 6,  # feed_pb2.FeedType.private_eyes
        "reposted": 7,  # feed_pb2.FeedType.reposted
        "top_of_leaderboard": 8,  # feed_pb2.FeedType.top_of_leaderboard
    }

    hook = BigQueryHook(use_legacy_sql=False)
    print(f"Executing query: {query_foryou}")

    posts = {}
    df = hook.get_pandas_df(sql=query_foryou)
    reasons = defaultdict(int)
    reason_enums = defaultdict(int)

    for _, row in df.iterrows():
        profile_id = row["profile_id"]

        # Print length of posts for this profile
        print(f"Profile {profile_id}: row['posts'] length = {len(row['posts'])}")

        # Create simplified data structure (without protobuf for now)
        post_data = {
            "ids": [],
            "feed_index": []
        }

        for post in row["posts"]:
            post_data["ids"].append(post["post_id"])
            reasons[post.get("reason", "suggested")] += 1
            reason_enum = reason_to_enum.get(post.get("reason", "suggested"), 0)
            reason_enums[reason_enum] += 1

            feed_item = {
                "id": post["post_id"],
                "type": reason_enum,
                "label": post.get("label", "")
            }
            post_data["feed_index"].append(feed_item)

        # Convert to JSON and base64 encode (simplified version of protobuf serialization)
        serialized_data = json.dumps(post_data).encode('utf-8')
        posts[profile_id] = base64.b64encode(serialized_data).decode('utf-8')

    print(f"reasons: {reasons}")
    print(f"reasons_enums: {reason_enums}")
    print(f"BigQuery User Rows: {len(posts)}")

    # Store in XCom for next task
    return posts

def write_to_bigtable_task(**context):
    """Write user post data to Bigtable"""
    # Get data from previous task
    posts = context['task_instance'].xcom_pull(task_ids='read_user_post_data')

    if not posts:
        print("No data received from previous task")
        return

    print(f"Number of Profiles to write: {len(posts)}")

    # Bigtable configuration
    bt_instance_id = "profiles"
    bt_table_id = "profiles"
    column_family_postids = "postids"
    column_feed_pb_index = "4up"

    # Use Bigtable Hook for connection
    hook = BigtableHook()
    client = hook.get_client(project_id=project_id)
    instance = client.instance(bt_instance_id)
    table = instance.table(bt_table_id)

    # Write data to Bigtable
    for profile_id, row_value_pb in posts.items():
        row_key = f"{profile_id}".encode()
        bigtable_row = table.row(row_key)
        bigtable_row.set_cell(column_family_postids, column_feed_pb_index, row_value_pb)
        bigtable_row.commit()

    print(f"Successfully wrote {len(posts)} profiles to Bigtable")

# Define tasks
start = DummyOperator(
    task_id='start',
    dag=dag,
)

read_data_task = PythonOperator(
    task_id='read_user_post_data',
    python_callable=read_user_post_data,
    provide_context=True,
    dag=dag,
)

write_bigtable_task = PythonOperator(
    task_id='write_to_bigtable',
    python_callable=write_to_bigtable_task,
    provide_context=True,
    dag=dag,
)

end = DummyOperator(
    task_id='end',
    dag=dag,
)

# Define task dependencies
start >> read_data_task >> write_bigtable_task >> end
