import datetime

from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.operators.python_operator import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, ShortCircuitOperator, PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator, BigQueryCreateEmptyTableOperator
from airflow.providers.google.cloud.operators.dataproc import DataprocSubmitJobOperator
from airflow.providers.google.cloud.hooks.bigquery import <PERSON><PERSON><PERSON>yHook
from airflow.utils.dates import days_ago
from airflow.decorators import task
from slack_alert import dag_fail_slack_alert

date=days_ago(1).strftime('%Y-%m-%d')
date_nodash=days_ago(1).strftime('%Y%m%d')
today_date=days_ago(0).strftime('%Y-%m-%d')
today_nodash=days_ago(0).strftime('%Y%m%d')
floored_minutes = (datetime.datetime.now().minute // 15) * 15
run_time = datetime.datetime.now().replace(minute=floored_minutes).strftime("%H%M")
project_id="butterflies-ai"
cluster_region="us-central1"
#cluster_name="cluster-data2"
cluster_name="cluster-data3"
job_dir="gs://dataproc-butterflies/bt-jobs"
copy_daily="gs://dataproc-butterflies/transfer/copy-daily.py"
copy_entire="gs://dataproc-butterflies/transfer/copy-entire.py"
copy_supabase="gs://dataproc-butterflies/transfer/copy-supabase.py"
copy_db="gs://dataproc-butterflies/transfer/copy-db.py"
jar_postgresql="gs://dataproc-butterflies/postgresql-42.7.0.jar"

default_args = {
    "owner": "Mehrdad",
    "depends_on_past": False,
    "email": "<EMAIL>",
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(minutes=5),
    "start_date": days_ago(1),
}

dag = DAG(
    "update_posts_feed",
    description='Update the feeds',
    catchup=False,
    default_args=default_args,
    schedule_interval='30 1-23 * * *',
    #schedule_interval='0,30 1-23 * * *',
    on_failure_callback= dag_fail_slack_alert,
)

start = DummyOperator(task_id='start', dag=dag)
end = DummyOperator(task_id='end', dag=dag, trigger_rule='none_failed_min_one_success')

data_ready = DummyOperator(
    task_id='data_ready',
    trigger_rule='none_failed_min_one_success',
    dag=dag,
)

posts_ready = DummyOperator(
    task_id='posts_ready',
    trigger_rule='none_failed_min_one_success',
    dag=dag,
)

begin_feed = DummyOperator(
    task_id='begin_feed',
    trigger_rule='none_failed_min_one_success',
    dag=dag,
)

def today_post_embeddings(table_name: str):
    return BigQueryExecuteQueryOperator(
        task_id=f"today_{table_name}",
        destination_dataset_table=f"butterflies-ai.db_daily.{table_name}_{today_nodash}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_TRUNCATE',
        params={'table_posts': f"db.posts_{today_nodash}"},
        dag=dag,
    )

def user_post_distance(table_name: str):
    return BigQueryExecuteQueryOperator(
        task_id=f"{table_name}",
        destination_dataset_table=f"butterflies-ai.db_computed.{table_name}_{today_nodash}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_APPEND',
        params={'today_nodash': f"{today_nodash}", 'yesterday_nodash': f"{date_nodash}"},
        dag=dag,
    )

def copy_table(table_name: str, method: str):
    return DataprocSubmitJobOperator(
        task_id=table_name,
        job={
            "placement": {"cluster_name": cluster_name},
            "pyspark_job": {
                "main_python_file_uri": copy_daily,
                "jar_file_uris": [jar_postgresql],
                "args": [table_name, today_date, method],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def copy_entire_table(table_name: str, dataset_name: str):
    return DataprocSubmitJobOperator(
        task_id=f"{table_name}",
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': 'copy_entire_table', 'table': table_name},
            "pyspark_job": {
                "main_python_file_uri": copy_entire,
                "jar_file_uris": [jar_postgresql],
                "args": [table_name, dataset_name],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def copy_table_daily(table_name: str, timestamp_field: str):
    return DataprocSubmitJobOperator(
        task_id=table_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': 'copy_table_daily', 'table': table_name},
            "pyspark_job": {
                "main_python_file_uri": copy_supabase,
                "jar_file_uris": [jar_postgresql],
                "args": [table_name, today_date, "daily", timestamp_field],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def copy_daily_increment(table_name: str):
    return DataprocSubmitJobOperator(
        task_id=table_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': 'copy_increment', 'table': f"{table_name}_{today_nodash}"},
            "pyspark_job": {
                "main_python_file_uri": copy_db,
                "jar_file_uris": [jar_postgresql],
                'args': ["--db_table", table_name, "--bq_table", f"{table_name}_{today_nodash}", "--date", today_date, "--config", '{}'],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def copy_increment(table_name: str):
    return DataprocSubmitJobOperator(
        task_id=table_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': 'copy_increment', 'table': table_name},
            "pyspark_job": {
                "main_python_file_uri": copy_db,
                "jar_file_uris": [jar_postgresql],
                'args': ["--db_table", table_name, "--bq_table", table_name, "--date", today_date, "--config", '{}'],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def run_job_noparam(job_name: str):
    return DataprocSubmitJobOperator(
        task_id=job_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': job_name},
            "pyspark_job": {
                "main_python_file_uri": f"{job_dir}/{job_name}.py",
                "python_file_uris": ["gs://dataproc-butterflies/protos.zip"],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def run_job(job_name: str):
    return DataprocSubmitJobOperator(
        task_id=job_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': job_name},
            "pyspark_job": {
                "main_python_file_uri": f"{job_dir}/{job_name}.py",
                "python_file_uris": ["gs://dataproc-butterflies/protos.zip"],
                "args": [date],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def run_job_int(job_name: str, days_ago: str):
    return DataprocSubmitJobOperator(
        task_id=job_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': job_name},
            "pyspark_job": {
                "main_python_file_uri": f"{job_dir}/{job_name}.py",
                #"properties": ["spark.driver.memory=4g","spark.executor.memory=4g"],
                "jar_file_uris": [jar_postgresql],
                "args": [date, days_ago],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def feed(table_name: str, feed_days_ago: int):
    feed_start_date_nodash = days_ago(feed_days_ago).strftime('%Y%m%d')
    return BigQueryExecuteQueryOperator(
        task_id=f"{table_name}",
        destination_dataset_table=f"butterflies-ai.db_computed.{table_name}_{today_nodash}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_TRUNCATE',
        params={'date_nodash': f"{today_nodash}", 'days_ago': f"{feed_days_ago}", 'start_date_nodash': f"{feed_start_date_nodash}"},
        dag=dag,
    )

def run_code(job_name: str, ts_nodash: str, ds_nodash: str):
    run_id = run_time #["11", "15"][1] #ts_nodash.split('T')[1]
    return DataprocSubmitJobOperator(
        task_id=job_name,
        job={
            "placement": {"cluster_name": "cluster-data1"},
            "pyspark_job": {
                "main_python_file_uri": f"{job_dir}/{job_name}.py",
                "args": [run_id, ds_nodash],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

def run_query(table_name: str):
    return BigQueryExecuteQueryOperator(
        task_id=f"{table_name}",
        destination_dataset_table=f"butterflies-ai.db_computed.{table_name}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_TRUNCATE',
        dag=dag,
    )

def run_query_append(table_name: str, ts_nodash: str, ds_nodash: str):
    run_id = run_time #["11", "15"][1] #ts_nodash.split('T')[1]
    return BigQueryExecuteQueryOperator(
        task_id=f"{table_name}",
        destination_dataset_table=f"butterflies-ai.db_computed.{table_name}_{ds_nodash}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_APPEND',
        params={'date_nodash': f"{today_nodash}", 'run_id': run_id},
        dag=dag,
    )

def run_query_append_yesterday(table_name: str, ts_nodash: str, ds_nodash: str):
    run_id = run_time #["11", "15"][1] #ts_nodash.split('T')[1]
    return BigQueryExecuteQueryOperator(
        task_id=f"{table_name}",
        destination_dataset_table=f"butterflies-ai.db_computed.{table_name}_{ds_nodash}",
        sql=f"{table_name}.sql",
        use_legacy_sql=False,
        write_disposition='WRITE_APPEND',
        params={'date_nodash': f"{date_nodash}", 'run_id': run_id},
        dag=dag,
    )

def decide_to_run(**kwargs):
    execution_date = kwargs['execution_date']
    current_minute = execution_date.minute
    current_hour = execution_date.hour
    if current_minute == 30 and current_hour == 23:
        return 'end'
    else:
        return 'start'

def decide_which_data_path(**kwargs):
    execution_date = kwargs['execution_date']
    current_minute = execution_date.minute
    if current_minute == 30:
        return 'data_ready'
    else:
        return 'full_data_route'

def decide_which_job_path(**kwargs):
    execution_date = kwargs['execution_date']
    current_minute = execution_date.minute
    if current_minute == 30:
        return 'full_job_route' #'begin_feed'
    else:
        return 'full_job_route'

def ensure_table_exists(table_name: str, schema: list):
    return BigQueryCreateEmptyTableOperator(
        task_id=f"ensure_table_exists_{table_name}",
        project_id="butterflies-ai",
        dataset_id="db_computed",
        table_id=f"{table_name}_{today_nodash}",
        schema_fields=schema,
        exists_ok=True,
        dag=dag,
    )

def create_object_table_fn(**keyargs):
    hook = BigQueryHook(use_legacy_sql=False)
    uris_query = """
      SELECT SPLIT(ARRAY_REVERSE(SPLIT(media_url, '/'))[OFFSET(0)], '.')[OFFSET(0)] uri
      FROM db.posts_{today_nodash}
      WHERE id NOT IN (SELECT post_id FROM `db_computed.post_image_embeddings_*` WHERE post_id IS NOT NULL)
        and media_url is not null
        and visibility = 'public'
      limit 9999;
    """.format(today_nodash = f'{today_nodash}')
    print (uris_query)
    uris = hook.get_pandas_df(sql=uris_query)['uri'].tolist()
    uris_str = ',\n'.join(["'gs://butterflies-images-v1-us/orig/%s.png'" % i for i in uris])

    external_table_query="""
        CREATE OR REPLACE EXTERNAL TABLE `db_computed.tmp_images_object_table`
        WITH CONNECTION `us-central1.image_embedding`
        OPTIONS(
          object_metadata = 'SIMPLE',
          uris = [{uris_str}]
        );
    """.format(uris_str = uris_str)
    print (external_table_query)
    hook.run_query(external_table_query)


create_object_table = PythonOperator(
    task_id='create_object_table',
    python_callable=create_object_table_fn,
    dag=dag,
)

image_embedding_table_schema = [
    {"name": "post_id", "type": "INT64", "mode": "NULLABLE"},
    {"name": "image_embedding", "type": "FLOAT64", "mode": "REPEATED"},
    {"name": "centroid_id", "type": "INT64", "mode": "NULLABLE"},
    {"name": "centroid_distance", "type": "FLOAT64", "mode": "NULLABLE"},
    {"name": "nearest_centroids_distance", "type": "RECORD", "mode": "REPEATED", "fields": [{"name": "CENTROID_ID", "type": "INT64", "mode":"NULLABLE"}, {"name": "DISTANCE", "type": "FLOAT64", "mode": "NULLABLE"}]}
]

skip_or_run = BranchPythonOperator(
    task_id='skip_or_run',
    python_callable=decide_to_run,
    provide_context=True,
    dag=dag
)

# data_branch = BranchPythonOperator(
#     task_id='data_branch',
#     python_callable=decide_which_data_path,
#     provide_context=True,
#     dag=dag
# )

#full_data_route = DummyOperator(task_id='full_data_route', dag=dag)
#quick_data_route = DummyOperator(task_id='quick_data_route', dag=dag)

job_branch = BranchPythonOperator(
    task_id='job_route',
    python_callable=decide_which_job_path,
    provide_context=True,
    dag=dag
)

full_job_route = DummyOperator(task_id='full_job_route', dag=dag)
#quick_job_route = DummyOperator(task_id='quick_job_route', dag=dag)

#bot_post_comments = run_query_append("bot_post_comments", "{{ ts_nodash }}", "{{ ds_nodash }}")
#bot_profile_follow = run_query_append_yesterday("bot_profile_follow", "{{ ts_nodash }}", "{{ ds_nodash }}")
#bot_comment_queue = run_code("bot-comment-queue", "{{ ts_nodash }}", "{{ ds_nodash }}")
#bot_profile_queue = run_code("bot-profile-queue", "{{ ts_nodash }}", "{{ ds_nodash }}")

skip_or_run >> [start, end]
#start >> data_branch >> quick_data_route >> data_ready
#start >> data_branch >> [full_data_route, data_ready]
#data_ready >> bot_post_comments >> bot_comment_queue >> end
#bot_post_comments >> bot_profile_follow >> bot_profile_queue >> end
#data_ready >> job_branch >> quick_job_route >> begin_feed

start >> copy_daily_increment("hide_lists") >> copy_daily_increment("profiles") >> copy_daily_increment("post_likes") >> copy_daily_increment("post_mentions") >> data_ready
start >> copy_daily_increment("blocked_lists") >> copy_daily_increment("post_comments") >> copy_daily_increment("bots") >> copy_daily_increment("reposts") >> data_ready
start >> copy_daily_increment("post_reads") >> copy_table_daily("post_reviews", "created_at") >> copy_increment("profile_settings")  >> copy_daily_increment("post_bookmarks") >> data_ready
start >> copy_daily_increment("posts") >> posts_ready
start >> copy_daily_increment("leaderboard_submissions") >> data_ready
posts_ready >> copy_daily_increment("post_comment_likes") >> copy_daily_increment("admin_action_logs") >> data_ready
posts_ready >> ensure_table_exists("post_image_embeddings", image_embedding_table_schema) >> create_object_table >> run_query("embed_post_images") >> run_query_append('post_image_clusters', "{{ ts_nodash }}", "{{ ds_nodash }}") >>  run_query_append("post_image_embeddings", "{{ ts_nodash }}", "{{ ds_nodash }}") >> begin_feed 
#start >> copy_table_daily("posts", "updated_at") >> data_ready

post_like_comment = run_query("post_like_comment")
data_ready >> run_query("profile_score") >> run_job_noparam("bt-profile-score") >> end
data_ready >> job_branch >> [full_job_route, begin_feed]
data_ready >> run_job_noparam("bt-bq-posts") >> post_like_comment >> begin_feed
post_like_comment >> run_job_noparam("generate-comments-likes") >> end

full_job_route >> begin_feed
#full_job_route >> today_post_embeddings("post_embeddings") >> user_post_distance("user_post_distance") >> begin_feed

begin_feed >> feed("feed_following", 60) >> run_job("bt-knn-following-id") >> end
begin_feed >> feed("feed_foryou", 14) >> run_job("bt-knn-foryou-id") >> end
begin_feed >> feed("feed_explore", 14) >> run_job("bt-knn-explore-id") >> end
begin_feed >> feed("feed_reels", 30) >> run_job("bt-knn-reels-id") >> end