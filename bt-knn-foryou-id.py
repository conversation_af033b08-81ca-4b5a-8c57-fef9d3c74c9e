import sys
import json
import os
import random
import base64
import feed_pb2
from collections import defaultdict
from datetime import datetime, timedelta
from pyspark.sql import SparkSession
from google.cloud import bigtable
from google.cloud import bigquery
from google.cloud import secretmanager

project_id = "butterflies-ai"
def main():
    if len(sys.argv) < 2:
        print("Usage: <script>.py <posts_from_date>") #example: 2024-02-06
        sys.exit(1)

    yesterday_date = datetime.strptime(sys.argv[1], "%Y-%m-%d")
    today_nodash = (yesterday_date+timedelta(days=1)).strftime('%Y%m%d')

    only_new=False
    if len(sys.argv) >= 3:
        only_new=(sys.argv[2]=="only_new")
    user_post = read_user_post(today_nodash, only_new)

    bt_data = {}
    for profile_id, post_ids in user_post.items():
        bt_data[str(profile_id)] = post_ids
    print(f"Number of Profiles: {len(bt_data)}")

    spark = SparkSession.builder \
        .appName(f"Write posts to BigTable") \
        .config("temporaryGcsBucket", "temp-7d") \
        .getOrCreate()

    rdd = spark.sparkContext.parallelize(bt_data.items(), 10)
    rdd.foreachPartition(write_to_bigtable)

    spark.stop()

def write_to_bigtable(bt_iterator):
    bt_instance_id = "profiles"
    bt_table_id = "profiles"
    column_family_postids = "postids"
    column_feed_index = "4u"
    column_feed_pb_index = "4up"
    client = bigtable.Client(project=project_id, admin=True)
    instance = client.instance(bt_instance_id)
    table = instance.table(bt_table_id)
    for bt_pair in bt_iterator:
        profile_id = bt_pair[0]
        row_key = f"{profile_id}".encode()
        row_value_pb = bt_pair[1]
        bigtable_row = table.row(row_key)
        bigtable_row.set_cell(column_family_postids, column_feed_pb_index, row_value_pb)

        bigtable_row.commit()      
    client.close()

def access_secret_version(secret_id):
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
    response = client.access_secret_version(name=name)
    payload = response.payload.data.decode("UTF-8")
    return payload

def read_user_post(date_nodash, only_new):
    query_foryou = f"""
        --bt-knn-foryou-id.py
        with post_reads as (
        select profile_id, max(created_at) as created_at, min(DATE_DIFF(date(CURRENT_TIMESTAMP()), date(created_at), DAY)) as days_ago
        from `db.post_reads`
        group by 1
        ), profiles as (
        select profile_id
        from (select id as profile_id from db.profiles where created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        union all
        select profile_id from post_reads where FLOOR(RAND() * days_ago / 7)=0
        union all 
        select -1 as profile_id
        union all 
        select -2 as profile_id
        union all 
        select -3 as profile_id
        )
        group by 1
        )

        SELECT f.profile_id, array_agg(struct(post_id, reason, label) order by score desc limit 400) as posts
        FROM `butterflies-ai.db_computed.feed_foryou_{date_nodash}` f
        join profiles p on p.profile_id = f.profile_id
        group by 1;
    """
    reason_to_enum = {
        "suggested": feed_pb2.FeedType.suggested,
        "created_by": feed_pb2.FeedType.created_by,
        "followed_by": feed_pb2.FeedType.followed_by,
        "similar_to": feed_pb2.FeedType.similar_to,
        "tagged_by": feed_pb2.FeedType.tagged_by,
        "trending": feed_pb2.FeedType.trending,
        "private_eyes": feed_pb2.FeedType.private_eyes,
        "reposted": feed_pb2.FeedType.reposted,
        "top_of_leaderboard": feed_pb2.FeedType.top_of_leaderboard,
    }

    client = bigquery.Client()
    print(query_foryou)
    posts = {}
    query_job = client.query(query_foryou, location="us-central1")
    results = query_job.result()
    reasons = defaultdict(int)
    reason_enums = defaultdict(int)
    for row in results:
        profile_id = row["profile_id"]
        id_array = feed_pb2.IdArray()
        for post in row.posts:
            id_array.ids.append(post["post_id"])
            reasons[post.get("reason", "suggested")] += 1
            reason_enums[reason_to_enum.get(post.get("reason", "suggested"), feed_pb2.FeedType.suggested)] += 1
            feed_item = feed_pb2.FeedIndex(
                id=post["post_id"],
                type=reason_to_enum.get(post.get("reason", "suggested"), feed_pb2.FeedType.suggested),
                label=post.get("label", "")
            )
            id_array.feed_index.append(feed_item)
        serialized_data = id_array.SerializeToString()
        posts[profile_id] = base64.b64encode(serialized_data).decode('utf-8')

    print(f"reasons: {reasons}") 
    print(f"reasons_enums: {reason_enums}") 
    print(f"BigQuery User Rows: {len(posts)}")
    return posts

if __name__ == "__main__":
    main()
