import datetime
import json
import re
from datetime import timed<PERSON>ta

from airflow import D<PERSON>
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.utils.dates import days_ago
from slack_alert import dag_fail_slack_alert

# Date variables
date = days_ago(1).strftime('%Y-%m-%d')
date_nodash = days_ago(1).strftime('%Y%m%d')
today_date = days_ago(0).strftime('%Y-%m-%d')
today_nodash = days_ago(0).strftime('%Y%m%d')

# Configuration
project_id = "butterflies-ai"

default_args = {
    "owner": "Mehrdad",
    "depends_on_past": False,
    "email": "<EMAIL>",
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(minutes=5),
    "start_date": days_ago(1),
}

dag = DAG(
    "copy_posts_only_embedded",
    description='Copy posts table only - embedded logic from copy-db.py',
    catchup=False,
    default_args=default_args,
    schedule_interval=None,  # Manual trigger only
    on_failure_callback=dag_fail_slack_alert,
)

# Embedded functions from copy-db.py
def load_config_from_string(config_string):
    default_config = {
        "db_table": "",
        "db_dataset": "public",
        "db_select": "*",
        "db_where": "",
        "bq_table": "",
        "bq_dataset": "db",
        "bq_where": "",
        "bq_mode": "append",  # append, overwrite
        "copy_mode": "incr",  # incr, time-incr, entire, daily
        "incr_minutes": 120,
    }

    config = json.loads(config_string)
    final_config = {**default_config, **config}
    if (final_config["db_table"] != "" and final_config["bq_table"] == ""):
        final_config["bq_table"] = final_config["db_table"]
    return final_config

def access_secret_version(secret_id):
    from google.cloud import secretmanager
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
    response = client.access_secret_version(name=name)
    payload = response.payload.data.decode("UTF-8")
    return payload

def read_db(spark, db_table, db_select, db_where):
    db_password = access_secret_version("SUPABASE_PASSWORD")
    db_url = "*******************************************************************"
    db_properties = {
        "user": "postgres.ciqehpcxkkhdjdxolvho",
        "password": db_password,
        "driver": "org.postgresql.Driver",
        "timeout": "120"
    }
    if db_where != "":
        db_table = f"(SELECT {db_select} FROM {db_table} WHERE {db_where}) AS tmp"

    print(f"DB: -----> {db_table}")
    return spark.read.jdbc(url=db_url, table=db_table, properties=db_properties)

def write_bq(df, bq_table, bq_mode):
    df.write \
        .format("bigquery") \
        .option("table", f"{project_id}:{bq_table}") \
        .mode(bq_mode) \
        .save()
    return

def read_id(bq_table, bq_where):
    from google.cloud import bigquery
    query = f"""
    --copy-db.py
    select max(id) as id FROM `{project_id}.{bq_table}` WHERE {bq_where}
    """
    client = bigquery.Client()
    print(query)
    query_job = client.query(query, location="us-central1")
    results = query_job.result()
    max_id = 0
    for row in results:
        max_id = row.id
    if max_id == None:
        max_id = 0
    return max_id

def is_bigquery_table_available(bq_table):
    from google.cloud import bigquery
    from google.api_core.exceptions import NotFound
    client = bigquery.Client(project=project_id)
    table_ref = f"{project_id}.{bq_table}"

    try:
        client.get_table(table_ref)
        print(f"Table {table_ref} exists.")
        return True
    except NotFound:
        print(f"Table {table_ref} does not exist.")
        return False

def copy_posts_main(**context):
    """Main function embedded from copy-db.py for posts table"""
    from pyspark.sql import SparkSession

    # Configuration for posts table
    config = {
        "db_table": "posts",
        "db_dataset": "public",
        "db_select": "*",
        "db_where": "",
        "bq_table": f"posts_{today_nodash}",
        "bq_dataset": "db",
        "bq_where": "",
        "bq_mode": "append",
        "copy_mode": "incr",
        "incr_minutes": 120,
    }

    date_param = today_date

    db_table = f"{config['db_dataset']}.{config['db_table']}"
    bq_table = f"{config['bq_dataset']}.{config['bq_table']}"

    print(f"--> Copy ({config['copy_mode']}) from {db_table} to {bq_table} ({config['bq_mode']})")
    bq_where = config['bq_where']
    db_select = config['db_select']
    db_where = config['db_where']

    if config['copy_mode'] == "incr":
        bq_where = f"created_at >= '{date_param}'"
        max_id = 0
        if is_bigquery_table_available(bq_table):
            max_id = read_id(bq_table, bq_where)
        else:
            print(f"Table: {bq_table} not found. So, no id is being deduped")
        db_where = f"date(created_at)='{date_param}' and id > {max_id}"
        if config['db_table'] == "posts":
            db_where = f"date(created_at)='{date_param}' and id > {max_id} and visibility not in ('hidden', 'archived', 'draft')"

    spark = SparkSession.builder \
        .appName(f"{db_table}: PostgreSQL to BigQuery") \
        .config("temporaryGcsBucket", "temp-7d") \
        .getOrCreate()

    df_db = read_db(spark, db_table, db_select, db_where)

    diff_df = df_db
    diff_report = f"whole db read: {df_db.count()}"
    write_bq(diff_df, bq_table, config['bq_mode'])

    spark.stop()
    print(diff_report)

# Create tasks
start = DummyOperator(
    task_id='start',
    dag=dag,
)

posts_ready = DummyOperator(
    task_id='posts_ready',
    dag=dag,
)

end = DummyOperator(
    task_id='end',
    dag=dag,
)

# Create the posts copy task with embedded logic
copy_posts_task = PythonOperator(
    task_id='posts',
    python_callable=copy_posts_main,
    provide_context=True,
    dag=dag,
)

# Define the task dependencies - exactly as you selected
start >> copy_posts_task >> posts_ready >> end
