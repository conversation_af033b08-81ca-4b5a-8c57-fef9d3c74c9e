import datetime

from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.providers.google.cloud.operators.dataproc import DataprocSubmitJobOperator
from airflow.utils.dates import days_ago
from slack_alert import dag_fail_slack_alert

# Date variables
date = days_ago(1).strftime('%Y-%m-%d')
date_nodash = days_ago(1).strftime('%Y%m%d')
today_date = days_ago(0).strftime('%Y-%m-%d')
today_nodash = days_ago(0).strftime('%Y%m%d')

# Configuration
project_id = "butterflies-ai"
cluster_region = "us-central1"
cluster_name = "cluster-data3"
copy_db = "gs://dataproc-butterflies/transfer/copy-db.py"
jar_postgresql = "gs://dataproc-butterflies/postgresql-42.7.0.jar"

default_args = {
    "owner": "Mehr<PERSON>",
    "depends_on_past": False,
    "email": "<EMAIL>",
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(minutes=5),
    "start_date": days_ago(1),
}

dag = DAG(
    "copy_posts_only",
    description='Copy posts table only - daily increment',
    catchup=False,
    default_args=default_args,
    schedule_interval=None,  # Manual trigger only
    on_failure_callback=dag_fail_slack_alert,
)

def copy_daily_increment(table_name: str):
    return DataprocSubmitJobOperator(
        task_id=table_name,
        job={
            "placement": {"cluster_name": cluster_name},
            'labels': {'job': 'copy_increment', 'table': f"{table_name}_{today_nodash}"},
            "pyspark_job": {
                "main_python_file_uri": copy_db,
                "jar_file_uris": [jar_postgresql],
                'args': ["--db_table", table_name, "--bq_table", f"{table_name}_{today_nodash}", "--date", today_date, "--config", '{}'],
            },
        },
        region=cluster_region,
        project_id=project_id,
        dag=dag,
    )

# Create tasks
start = DummyOperator(
    task_id='start',
    dag=dag,
)

posts_ready = DummyOperator(
    task_id='posts_ready',
    dag=dag,
)

end = DummyOperator(
    task_id='end',
    dag=dag,
)

# Create the posts copy task
copy_posts_task = copy_daily_increment("posts")

# Define the task dependencies - exactly as you selected
start >> copy_posts_task >> posts_ready >> end
