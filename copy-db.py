import argparse
import json
import re
import sys
from datetime import datetime, timedelta
from google.cloud import bigquery
from google.cloud import secretmanager
from pyspark.sql import SparkSession
from pyspark.sql.functions import col
from google.api_core.exceptions import NotFound

# example command line:
# gcloud dataproc jobs submit pyspark copy-db.py --cluster=cluster-data2  --region=us-central1 --jars=gs://dataproc-butterflies/postgresql-42.7.0.jar -- --config '{"db_table": "blocked_lists", "bq_dataset": "mehrdad_dev"}'
project_id = "butterflies-ai"
def load_config_from_string(config_string):
    default_config = {
        "db_table": "",
        "db_dataset": "public",
        "db_select": "*",
        "db_where": "",
        "bq_table": "",
        "bq_dataset": "db",
        "bq_where": "",
        "bq_mode": "append", #append, overwrite
        "copy_mode": "incr", #incr, time-incr, entire, daily
        "incr_minutes": 120,
    }

    config = json.loads(config_string)
    final_config = {**default_config, **config}
    if (final_config["db_table"]!="" and final_config["bq_table"]==""):
        final_config["bq_table"] = final_config["db_table"]
    return final_config

def read_db(spark, db_table, db_select, db_where):
    db_password: str = access_secret_version("SUPABASE_PASSWORD")
    db_url = "*******************************************************************"
    db_properties = {
        "user": "postgres.ciqehpcxkkhdjdxolvho",
        "password": db_password,
        "driver": "org.postgresql.Driver",
        "timeout": "120"
    }
    if db_where != "":
        db_table = f"(SELECT {db_select} FROM {db_table} WHERE {db_where}) AS tmp"

    print(f"DB: -----> {db_table}")
    return spark.read.jdbc(url=db_url, table=db_table, properties=db_properties)

def read_bq(spark, bq_table, bq_where):
    if bq_where == "":
        return spark.read.format('bigquery') \
            .option('project', project_id) \
            .option('table', f'{project_id}:{bq_table}') \
            .load()
    else:
        query = f"SELECT max(id) as id FROM `{project_id}.{bq_table}` WHERE {bq_where}"
        print(f"BQ: -----> {query}")
        return spark.read.format('bigquery') \
            .option("viewsEnabled", "true") \
            .option("materializationDataset", "db_computed") \
            .option('query', query) \
            .load()
        # return spark.read.format('bigquery') \
        #     .option('project', project_id) \
        #     .option('table', f'{project_id}:{bq_table}') \
        #     .option("filter", bq_where) \
        #     .load()

def write_bq(df, bq_table, bq_mode):
    df.write \
        .format("bigquery") \
        .option("table", f"{project_id}:{bq_table}") \
        .mode(bq_mode) \
        .save()
    return

def read_id(bq_table, bq_where):
    query = f"""
    --copy-db.py
    select max(id) as id FROM `{project_id}.{bq_table}` WHERE {bq_where}
    """
    client = bigquery.Client()
    print(query)
    query_job = client.query(query, location="us-central1")
    results = query_job.result()
    max_id = 0
    for row in results:
        max_id = row.id
    if max_id == None:
        max_id = 0
    return max_id

def get_input_table_name(output_table_name):
    pattern = r'^([a-zA-Z]+(?:_[a-zA-Z]+)*)_(\d{8})$'
    match = re.match(pattern, output_table_name)
    if match:
        return match.group(1)
    else:
        return output_table_name

def is_bigquery_table_available(bq_table):
    client = bigquery.Client(project=project_id)
    table_ref = f"{project_id}.{bq_table}"

    try:
        client.get_table(table_ref)
        print(f"Table {table_ref} exists.")
        return True
    except NotFound:
        print(f"Table {table_ref} does not exist.")
        return False

def main(config, date):
    db_table = f"{config['db_dataset']}.{config['db_table']}"
    bq_table = f"{config['bq_dataset']}.{config['bq_table']}"
    #f"{config['bq_dataset']}.{get_input_table_name(config['bq_table'])}"

    print(f"--> Copy ({config['copy_mode']}) from {db_table} to {bq_table} ({config['bq_mode']})")
    bq_where = config['bq_where']
    db_select = config['db_select']
    db_where = config['db_where']
    if config['copy_mode'] == "incr":
        bq_where = f"created_at >= '{date}'"
        max_id = 0
        if is_bigquery_table_available(bq_table):
            max_id = read_id(bq_table, bq_where)
        else:
            print(f"Table: {bq_table} not found. So, no id is being deduped")
        db_where = f"date(created_at)='{date}' and id > {max_id}"
        if config['db_table'] == "posts":
            db_where = f"date(created_at)='{date}' and id > {max_id} and visibility not in ('hidden', 'archived', 'draft')"

    spark = SparkSession.builder \
        .appName(f"{db_table}: PostgreSQL to BigQuery") \
        .config("temporaryGcsBucket", "temp-7d") \
        .getOrCreate()

    df_db = read_db(spark, db_table, db_select, db_where)

    diff_df = df_db
    diff_report = f"whole db read: {df_db.count()}"
    write_bq(diff_df, bq_table, config['bq_mode'])

    spark.stop()
    print (diff_report)

def access_secret_version(secret_id):
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
    response = client.access_secret_version(name=name)
    payload = response.payload.data.decode("UTF-8")
    return payload

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, required=False, help='JSON string with configuration')
    parser.add_argument('--db_table', type=str, required=True, help='Input Table')
    parser.add_argument('--bq_table', type=str, required=True, help='Output Table')
    parser.add_argument('--date', type=str, required=True, help='Date')
    parser.add_argument('--copy_mode', type=str, required=False, help='optional string')
    parser.add_argument('--bq_mode', type=str, required=False, help='optional string')
    parser.add_argument('--db_select', type=str, required=False, help='optional string')
    parser.add_argument('--db_where', type=str, required=False, help='optional string')
    parser.add_argument('--bq_dataset', type=str, required=False, help='optional string')
    args = parser.parse_args()

    config = load_config_from_string(args.config)
    config["db_table"]=args.db_table
    config["bq_table"]=args.bq_table
    if args.copy_mode is not None:
        config['copy_mode'] = args.copy_mode
    if args.bq_mode is not None:
        config['bq_mode'] = args.bq_mode
    if args.db_select is not None:
        config['db_select'] = args.db_select
    if args.db_where is not None:
        config['db_where'] = args.db_where
    if args.bq_dataset is not None:
        config['bq_dataset'] = args.bq_dataset
    date=args.date
    print(f"Date='{date}' and Config={config}")
    if (config["db_table"]=="" or date==""):
        print("no table is specified!")
        sys.exit(1)

    main(config, date)